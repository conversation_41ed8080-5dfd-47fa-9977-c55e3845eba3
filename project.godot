; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Outpost Assault"
run/main_scene="res://ui/main_menu.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[autoload]

Global="*res://autoloads/global.gd"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/stretch/mode="viewport"

[gui]

theme/custom="res://ui/ui_theme.tres"

[layer_names]

2d_physics/layer_1="infantry"
2d_navigation/layer_1="ground"
2d_physics/layer_2="tower"
2d_navigation/layer_2="air"
2d_physics/layer_3="projectile"
2d_physics/layer_4="tower_slot"
2d_physics/layer_5="objective"
2d_physics/layer_6="tank"
2d_physics/layer_7="helicopter"
avoidance/layer_1="ground"
avoidance/layer_2="air"
